

from lightning.pytorch import Trainer
import torch
from omegaconf import <PERSON>Conf, DictConfig, open_dict
from nemo.collections.asr.models import EncDecRNNTBPEModel
from nemo.utils import logging
import os
from nemo.collections.asr.parts.mixins.transcription import TranscribeConfig

import torchaudio
from custom import HFTokenizerAdapter
from dataset import get_audio_to_text_bpe_dataset_from_config
from typing import List, Union, Optional, Dict
from nemo.collections.common import tokenizers

class ASRModel(EncDecRNNTBPEModel):
    def _init__(self, cfg: DictConfig, trainer: Trainer = None):
        super().__init__(cfg, trainer=trainer)
        # Switch model to evaluation mode
        if hasattr(self, 'preprocessor'):
            if hasattr(self.preprocessor, 'featurizer') and hasattr(self.preprocessor.featurizer, 'dither'):
                self.preprocessor.featurizer.dither = 0.0
            if hasattr(self.preprocessor, 'featurizer') and hasattr(self.preprocessor.featurizer, 'pad_to'):
            
                self.preprocessor.featurizer.pad_to = 0

    def _setup_aggregate_tokenizer(self, tokenizer_cfg: DictConfig):
        # Prevent tokenizer parallelism (unless user has explicitly set it)
        if "TOKENIZERS_PARALLELISM" not in os.environ:
            os.environ["TOKENIZERS_PARALLELISM"] = "false"

        self.tokenizer_cfg = OmegaConf.to_container(
            tokenizer_cfg, resolve=True
        )  # type: dict

        # the aggregate tokenizer does not have one tokenizer_dir but multiple ones
        self.tokenizer_dir = None

        self.tokenizer_cfg.pop("dir", None)  # Remove tokenizer directory, if any
        # Remove tokenizer_type -- obviously if we are here, the type is 'agg'
        self.tokenizer_type = self.tokenizer_cfg.pop("type").lower()

        # the aggregate tokenizer should not have these
        self.hf_tokenizer_kwargs = {}
        self.tokenizer_cfg.pop("hf_kwargs", {})  # Remove HF tokenizer kwargs, if any

        logging.info("_setup_tokenizer: detected an aggregate tokenizer")
        # need to de-register any monolingual config items if they exist
        self._cleanup_monolingual_and_aggregate_config_and_artifacts_if_needed()

        # overwrite tokenizer type
        if hasattr(self, "cfg") and "tokenizer" in self.cfg:
            self.cfg.tokenizer = self.tokenizer_cfg
        tokenizers_dict = {}
        # init each of the monolingual tokenizers found in the config and assemble into  AggregateTokenizer
        for lang, tokenizer_config in self.tokenizer_cfg[
            self.AGGREGATE_TOKENIZERS_DICT_PREFIX
        ].items():
            (
                tokenizer,
                model_path,
                vocab_path,
                spe_vocab_path,
            ) = (
                self._make_tokenizer(tokenizer_config, lang)
                if lang != "vi"
                else (
                    HFTokenizerAdapter(tokenizer_config["dir"]),
                    None,
                    None,
                    None,
                )
            )

            tokenizers_dict[lang] = tokenizer
            if hasattr(self, "cfg"):
                with open_dict(self.cfg.tokenizer):
                    self.cfg.tokenizer[self.AGGREGATE_TOKENIZERS_DICT_PREFIX][lang][
                        "dir"
                    ] = self.tokenizer_cfg[self.AGGREGATE_TOKENIZERS_DICT_PREFIX][lang][
                        "dir"
                    ]
                    self.cfg.tokenizer[self.AGGREGATE_TOKENIZERS_DICT_PREFIX][lang][
                        "type"
                    ] = self.tokenizer_cfg[self.AGGREGATE_TOKENIZERS_DICT_PREFIX][lang][
                        "type"
                    ]

        if "custom_tokenizer" in tokenizer_cfg:
            # Class which implements this is usually a ModelPT, has access to Serializable mixin by extension
            self.tokenizer = self.from_config_dict(
                {
                    "_target_": tokenizer_cfg["custom_tokenizer"]["_target_"],
                    "tokenizers": tokenizers_dict,
                }
            )
        else:
            self.tokenizer = tokenizers.AggregateTokenizer(tokenizers_dict)

    def setup_training_data(self, train_data_config):
        self._shutdown_dataset(self._train_dl)
        super().setup_training_data(train_data_config)

    def setup_validation_data(self, val_data_config):
        self._shutdown_dataset(self._validation_dl)
        super().setup_validation_data(val_data_config)

    def setup_test_data(self, test_data_config):
        self._shutdown_dataset(self._test_dl)
        super().setup_test_data(test_data_config)

    def _setup_dataloader_from_config(self, config: Optional[Dict]):
        if config.get("is_custom", False):
            dataset = get_audio_to_text_bpe_dataset_from_config(
                config=config,
                global_rank=self.global_rank,
                world_size=self.world_size,
                tokenizer=self.tokenizer,
            )
            shuffle = config["shuffle"]
            if isinstance(dataset, torch.utils.data.IterableDataset):
                shuffle = False
            dataloader = torch.utils.data.DataLoader(
                dataset=dataset,
                batch_size=config["batch_size"],
                # sampler=batch_sampler,
                batch_sampler=None,
                collate_fn=dataset.collate_fn,
                drop_last=config.get("drop_last", False),
                shuffle=shuffle,
                num_workers=config.get("num_workers", 0),
                pin_memory=config.get("pin_memory", False),
            )
        else:
            dataloader = super()._setup_dataloader_from_config(config)
        return dataloader

    def on_train_end(self):
        super().on_train_end()
        self._shutdown_dataset(self._train_dl)
        self._shutdown_dataset(self._validation_dl)

    def on_test_end(self):
        super().on_test_end()
        self._shutdown_dataset(self._test_dl)

    def _shutdown_dataset(self, dataloader):
        if dataloader is None:
            return
        dataset = dataloader.dataset

        if isinstance(dataset, (list, tuple)):
            for d in dataset:
                if hasattr(d, "shutdown") and callable(d.shutdown):
                    d.shutdown()
        elif hasattr(dataset, "datasets") and isinstance(
            dataset.datasets, (list, tuple)
        ):
            for d in dataset.datasets:
                if hasattr(d, "shutdown") and callable(d.shutdown):
                    d.shutdown()
        if hasattr(dataset, "shutdown") and callable(dataset.shutdown):
            dataset.shutdown()
   
    def _transcribe_forward(self, input_signal, input_signal_length):
        encoded, encoded_len = self.forward(input_signal=input_signal, input_signal_length=input_signal_length)
        output = dict(encoded=encoded, encoded_len=encoded_len)
        return output

    def transcribe(self, input_paths, return_hypotheses=False,beam_size=0,num_workers=3):

        transcribe_cfg = TranscribeConfig(
                batch_size=1,
                return_hypotheses=return_hypotheses,
                num_workers=num_workers,   
            )
        input_paths=input_paths[0]
        audio,sr=torchaudio.load(input_paths)
        audio = audio.to(self.device)
        audio_len = torch.tensor([audio.shape[-1]], dtype=torch.int64).to(self.device)
        model_outputs=self._transcribe_forward(audio,audio_len)
        processed_outputs = self._transcribe_output_processing(model_outputs, transcribe_cfg)
        
        results = None
        if isinstance(processed_outputs, list):
            # Create a results of the same type as each element in processed_outputs
            if results is None:
                results = []

            results.extend(processed_outputs)

        elif isinstance(processed_outputs, dict):
            # Create a results of the same type as each element in processed_outputs
            if results is None:
                results = processed_outputs
            else:
                for k, v in processed_outputs.items():
                    results[k].extend(v)

        elif isinstance(processed_outputs, tuple):
            # Create a results of the same type as each element in processed_outputs
            if results is None:
                results = tuple([[] for _ in processed_outputs])

            # If nested list structure
            if isinstance(processed_outputs[0], list):
                for i, processed_output in enumerate(processed_outputs):
                    results[i].extend(processed_output)
            else:
                # If flat list structure
                if len(processed_outputs) != len(results):
                    raise RuntimeError(
                        f"The number of elements in the result ({len(results)}) does not "
                        f"match the results of the current batch ({len(processed_outputs)})."
                    )

                for i, processed_output in enumerate(processed_outputs):
                    results[i].append(processed_output)

        else:
            raise NotImplementedError(
                "Given output result for transcription is not supported. "
                "Please return a list of results, list of list of results, "
                "a dict of list of results, or "
                "a tuple of list of results."
            )
        return results