
from asr_model.model import ASRModel
import os
asr_model = ASRModel.load_from_checkpoint(checkpoint_path="asr_model/parakeet-tdt-0.6-vn--val_wer=0.0651-epoch=1-last.ckpt",map_location='cpu')
# asr_model = ASRModel.restore_from(restore_path="asr_model/parakeet-tdt-0.6-vn.nemo",map_location='cpu')
asr_model.eval()
asr_model.to('cuda:1')

asr_model.cfg.decoding

from omegaconf import OmegaConf, open_dict

decoding_cfg = asr_model.cfg.decoding
with open_dict(decoding_cfg):
    # decoding_cfg.strategy = 'greedy_batch'
    decoding_cfg.strategy = 'beam'
    decoding_cfg.beam.beam_size=5
    decoding_cfg.beam.return_best_hypothesis=True
    asr_model.change_decoding_strategy(decoding_cfg)


import torch
import torchaudio
import time

# Load file âm thanh thành tensor
audio_file = "/TMTAI/AI_MemBer/workspace/quanghadd/Wav2vec-270h/audio_test/ytb_en_vi/wav/_nuQ39Y4T5Q.0414.wav"
signal, sample_rate = torchaudio.load(audio_file)

# Move tensor to GPU
signal = signal.to('cuda:1')
signal_len = torch.tensor([signal.shape[1]], dtype=torch.int64).to('cuda:1')

# Đo thời gian
with torch.no_grad():

    start = time.time()

    logits, logit_lens = asr_model.forward(
        input_signal=signal,
        input_signal_length=signal_len
    )
    end = time.time()
    hyp = asr_model.decoding.rnnt_decoder_predictions_tensor(
        logits,
        logit_lens,
        return_hypotheses=False,
        partial_hypotheses=None,
    )
print(logits)

print(f"Forward + decode time: {end - start:.4f} seconds")


hyp

asr_model.tokenizer.offset_token_ids_by_token_id

asr_model.tokenizer.ids_to_text(a)

a=asr_model.tokenizer.text_to_ids("procrastination", "en")

asr_model.tokenizer.ids_to_tokens(a)

a

# start=time.time()
# _nuQ39Y4T5Q.0312.wav
hypotheses = asr_model.transcribe(
    ["/TMTAI/AI_MemBer/workspace/quanghadd/Wav2vec-270h/audio_test/ytb_en_vi/wav/_nuQ39Y4T5Q.0414.wav"], return_hypotheses=True, num_workers=3
)
# print(hypotheses)
if type(hypotheses[0]) == list:
    print(hypotheses[0][0].text)
else:
    print(hypotheses[0].text)



import lightning.pytorch as pl
import torch
from omegaconf import OmegaConf, DictConfig, open_dict
from lightning.pytorch.loggers import MLFlowLogger
from nemo.collections.asr.models import EncDecRNNTBPEModel
from nemo.core.config import hydra_runner
from nemo.utils import logging,model_utils
from nemo.utils.exp_manager import exp_manager
from nemo.utils.trainer_utils import resolve_trainer_cfg
import os
from custom import HFTokenizerAdapter
from dataset import get_audio_to_text_bpe_dataset_from_config
from typing import List, Union,Optional,Dict
from nemo.collections.common import tokenizers
from  nemo.collections.asr.parts.preprocessing.perturb import perturbation_types
import copy

import custom
from importlib import reload

reload(custom)
os.environ["CUDA_VISIBLE_DEVICES"] = "1"
asr_model=None

%load_ext autoreload
%autoreload 2

class ASRModel(EncDecRNNTBPEModel):

    def _setup_aggregate_tokenizer(self, tokenizer_cfg: DictConfig):
        # Prevent tokenizer parallelism (unless user has explicitly set it)
        if "TOKENIZERS_PARALLELISM" not in os.environ:
            os.environ["TOKENIZERS_PARALLELISM"] = "false"

        self.tokenizer_cfg = OmegaConf.to_container(
            tokenizer_cfg, resolve=True
        )  # type: dict

        # the aggregate tokenizer does not have one tokenizer_dir but multiple ones
        self.tokenizer_dir = None

        self.tokenizer_cfg.pop("dir", None)  # Remove tokenizer directory, if any
        # Remove tokenizer_type -- obviously if we are here, the type is 'agg'
        self.tokenizer_type = self.tokenizer_cfg.pop("type").lower()

        # the aggregate tokenizer should not have these
        self.hf_tokenizer_kwargs = {}
        self.tokenizer_cfg.pop("hf_kwargs", {})  # Remove HF tokenizer kwargs, if any

        logging.info("_setup_tokenizer: detected an aggregate tokenizer")
        # need to de-register any monolingual config items if they exist
        self._cleanup_monolingual_and_aggregate_config_and_artifacts_if_needed()

        # overwrite tokenizer type
        if hasattr(self, "cfg") and "tokenizer" in self.cfg:
            self.cfg.tokenizer = self.tokenizer_cfg
        tokenizers_dict = {}
        # init each of the monolingual tokenizers found in the config and assemble into  AggregateTokenizer
        for lang, tokenizer_config in self.tokenizer_cfg[
            self.AGGREGATE_TOKENIZERS_DICT_PREFIX
        ].items():
            (
                tokenizer,
                model_path,
                vocab_path,
                spe_vocab_path,
            ) = (
                self._make_tokenizer(tokenizer_config, lang)
                if lang != "vi"
                else (
                    HFTokenizerAdapter(tokenizer_config["dir"]),
                    None,
                    None,
                    None,
                )
            )

            tokenizers_dict[lang] = tokenizer
            if hasattr(self, "cfg"):
                with open_dict(self.cfg.tokenizer):
                    self.cfg.tokenizer[self.AGGREGATE_TOKENIZERS_DICT_PREFIX][lang][
                        "dir"
                    ] = self.tokenizer_cfg[self.AGGREGATE_TOKENIZERS_DICT_PREFIX][lang][
                        "dir"
                    ]
                    self.cfg.tokenizer[self.AGGREGATE_TOKENIZERS_DICT_PREFIX][lang][
                        "type"
                    ] = self.tokenizer_cfg[self.AGGREGATE_TOKENIZERS_DICT_PREFIX][lang][
                        "type"
                    ]

        if "custom_tokenizer" in tokenizer_cfg:
            # Class which implements this is usually a ModelPT, has access to Serializable mixin by extension
            self.tokenizer = self.from_config_dict(
                {
                    "_target_": tokenizer_cfg["custom_tokenizer"]["_target_"],
                    "tokenizers": tokenizers_dict,
                }
            )
        else:
            self.tokenizer = tokenizers.AggregateTokenizer(tokenizers_dict)

    def setup_training_data(self, train_data_config):
        self._shutdown_dataset(self._train_dl)
        super().setup_training_data(train_data_config)

    def setup_validation_data(self, val_data_config):
        self._shutdown_dataset(self._validation_dl)
        super().setup_validation_data(val_data_config)

    def setup_test_data(self, test_data_config):
        self._shutdown_dataset(self._test_dl)
        super().setup_test_data(test_data_config)

    def _setup_dataloader_from_config(self, config: Optional[Dict]):
        if config.get("is_custom", False):
            dataset = get_audio_to_text_bpe_dataset_from_config(
                config=config,
                global_rank=self.global_rank,
                world_size=self.world_size,
                tokenizer=self.tokenizer,
            )
            shuffle = config["shuffle"]
            if isinstance(dataset, torch.utils.data.IterableDataset):
                shuffle = False
            dataloader = torch.utils.data.DataLoader(
                dataset=dataset,
                batch_size=config["batch_size"],
                # sampler=batch_sampler,
                batch_sampler=None,
                collate_fn=dataset.collate_fn,
                drop_last=config.get("drop_last", False),
                shuffle=shuffle,
                num_workers=config.get("num_workers", 0),
                pin_memory=config.get("pin_memory", False),
            )
        else:
            dataloader = super()._setup_dataloader_from_config(config)
        return dataloader
    def on_train_end(self):
        super().on_train_end()
        self._shutdown_dataset(self._train_dl)
        self._shutdown_dataset(self._validation_dl)
    def on_test_end(self):
        super().on_test_end()
        self._shutdown_dataset(self._test_dl)
    def _shutdown_dataset(self, dataloader):
        if dataloader is None:
            return
        dataset = dataloader.dataset

        if isinstance(dataset, (list, tuple)):
            for d in dataset:
                if hasattr(d, "shutdown") and callable(d.shutdown):
                    d.shutdown()
        elif hasattr(dataset, "datasets") and isinstance(dataset.datasets, (list, tuple)):
            for d in dataset.datasets:
                if hasattr(d, "shutdown") and callable(d.shutdown):
                    d.shutdown()
        if hasattr(dataset, "shutdown") and callable(dataset.shutdown):
            dataset.shutdown()

cfg = OmegaConf.load("fastconformer_hybrid_tdt_ctc_bpe.yaml")
cfg = model_utils.convert_model_config_to_dict_config(cfg)
# cfg.trainer.strategy = "ddp_notebook"
# trainer = pl.Trainer(**resolve_trainer_cfg(cfg.trainer) )
if not asr_model:
    _cfg=copy.deepcopy(cfg)
    _cfg.model.train_ds=None
    _cfg.model.validation_ds=None
    _cfg.model.test_ds=None
    asr_model = ASRModel(_cfg.model)
    asr_model.maybe_init_from_pretrained_checkpoint(_cfg)
asr_model.change_vocabulary(cfg.model.tokenizer_agg, "agg")
asr_model.setup_training_data(cfg.model.train_ds)
asr_model.setup_validation_data(cfg.model.validation_ds)
asr_model.setup_test_data(cfg.model.test_ds)
#### asr_model.train()

from s3_storerity.s3_resource import S3Resource
from custom import HFTokenizerAdapter
from omegaconf import OmegaConf
from nemo.utils import model_utils

import torch


cfg = OmegaConf.load("fastconformer_hybrid_tdt_ctc_bpe.yaml")
cfg = model_utils.convert_model_config_to_dict_config(cfg)
s3_resource = S3Resource()
tokenizer = HFTokenizerAdapter(cfg.model.tokenizer_agg["langs"]["vi"]["dir"])
dataset = get_audio_to_text_bpe_dataset_from_config(
    config=cfg.model.train_ds,
    global_rank=0,
    world_size=1,
    tokenizer=tokenizer,
)


dataset.datasets[1].collate_fn

d

dataloader = torch.utils.data.DataLoader(
    dataset=dataset,
    # batch_size=cfg.model.train_ds["batch_size"],
    batch_size=2,
    # sampler=batch_sampler,
    batch_sampler=None,
    collate_fn=dataset.collate_fn,
    drop_last=cfg.model.train_ds.get("drop_last", False),
    shuffle=False,
    # num_workers=cfg.model.train_ds.get("num_workers", 0),
    pin_memory=cfg.model.train_ds.get("pin_memory", False),

)

from nemo.collections.asr.models.rnnt_models import EncDecRNNTModel
preprocessor = EncDecRNNTModel.from_config_dict(cfg.model.preprocessor)

from tqdm import tqdm
sample=None
for d in tqdm(dataloader):
    preprocessor(input_signal=d[0],length=d[1])
   

import fasttext
from huggingface_hub import hf_hub_download

class FastTextTagger:
    def __init__(self, model_repo="facebook/fasttext-language-identification", model_filename="model.bin", cache_dir="./model_checkpoint", vocab_file="data/vi_words.txt"):
        self.model_path = hf_hub_download(repo_id=model_repo, filename=model_filename, cache_dir=cache_dir)
        self.model = fasttext.load_model(self.model_path)
        with open(vocab_file) as file:
            self.vocab_vi = set(file.read().split())

    def tagging_label(self, sentence):
        words = sentence.split()
        tags, _ = self.model.predict(words)
        
        label_tag = []
        for i, (word, tag) in enumerate(zip(words, tags)):
            if tag[0] == "__label__vie_Latn":
                label_tag.append([word, "<vi>"])
            elif tag[0] == "__label__eng_Latn":
                label_tag.append([word, "<en>"])
            else:
                if word in self.vocab_vi:
                    ts, _ = self.model.predict(" ".join(words[max(0, i-1):i+2]))  # Predict 3-word window
                    label_tag.append([word, "<en>" if ts[0] == "__label__eng_Latn" else "<vi>"])
                else:
                    label_tag.append([word, "<en>"])
        
        
        label_vi = " ".join(["<en>" if tag == "<en>" else word for word, tag in label_tag])
        label_en = " ".join(["<vi>" if tag == "<vi>" else word for word, tag in label_tag])
        return label_vi, label_en
    def get_en(self, sentence):
        words = sentence.split()
        tags, _ = self.model.predict(words)
        
        label_tag = []
        for i, (word, tag) in enumerate(zip(words, tags)):
            if tag[0] == "__label__vie_Latn":
                label_tag.append([word, "<vi>"])
            elif tag[0] == "__label__eng_Latn":
                label_tag.append([word, "<en>"])
            else:
                if word in self.vocab_vi:
                    ts, _ = self.model.predict(" ".join(words[max(0, i-1):i+2]))  # Predict 3-word window
                    label_tag.append([word, "<en>" if ts[0] == "__label__eng_Latn" else "<vi>"])
                else:
                    label_tag.append([word, "<en>"])
        
        
        en=" ".join([word  for word, tag in label_tag if tag == "<en>"])
        return en

tag=FastTextTagger()

import pandas as pd
df=pd.read_csv("results_eva/yt_en_vi_160625.txt")


import evaluate 
wer=evaluate.load("wer")

wers=[]
for a in df.to_records(index=False):
    infer=tag.get_en(str(a[1]))
    pred=tag.get_en(str(a[2]))
    print("Từ tiếng Anh ban dầu: {}".format(infer))
    print("Từ tiếng Anh được dự đoán: {}".format(pred))
    print("-------------------------------")
    if infer and pred:
        w= wer.compute(references=[infer],predictions=[pred])
        wers.append(w)
    else:
        wers.append(1)
print(sum(wers) / len(wers)*100)