from minio import Minio
from minio.commonconfig import CopySource
from minio.error import S3Error
import io
import time
import os

from minio import Minio
from minio.deleteobjects import DeleteObject

from .config import Config

class MinioService:
    def __init__(self):
        self.config = Config()
        self.client = Minio(
            endpoint= self.config.endpoint,
            access_key= self.config.access_key,
            secret_key= self.config.secret_key,
            secure=False
        )
    
    def get_list_buckets(self, **kwargs):
        buckets = self.client.list_buckets()
        return buckets
    
    def put_file(self, **kwargs):
        bucket_name = kwargs.get("bucket_name")
        object_name = kwargs.get("object_name")
        file_path = kwargs.get("file_path")
        result = self.client.fput_object(
            bucket_name=bucket_name,
            object_name=object_name,
            file_path=file_path
        )
        print(f"Created {result.object_name} object; etag: {result.etag}, version: {result.version_id}")

    def put_folder(self, local_folder_path, bucket_name, prefix=""):
        for root, dirs, files in os.walk(local_folder_path):
            for file_name in files:
                # Full path to the local file
                local_file_path = os.path.join(root, file_name)
                # Object name in MinIO (with optional prefix)
                object_name = os.path.join(prefix, os.path.relpath(local_file_path, local_folder_path))
                # Upload file to MinIO
                self.put_file(
                    bucket_name=bucket_name,
                    object_name=object_name,
                    file_path=local_file_path
                )
                print(f"Uploaded {local_file_path} to {bucket_name}/{object_name}")
                
    def get(self, **kwargs):
        
        bucket_name = kwargs.get("bucket_name")
        object_name = kwargs.get("object_name")
        file_path = kwargs.get("file_path")
        self.client.fget_object(
            bucket_name=bucket_name,
            object_name=object_name,
            file_path=file_path
        )


    def delete_file(self, **kwargs):
        bucket_name = kwargs.get("bucket_name")
        object_name = kwargs.get("object_name")
        errors = self.client.remove_objects(
            bucket_name=bucket_name,
            delete_object_list=[
                DeleteObject(obj)
                for obj in object_name
            ]
        )
        for error in errors:
            print(f"Error occurred when deleting object: {error}")

    def delete_folder(self, bucket_name, prefix):
        """Deletes all objects under a specific prefix (folder) in MinIO."""
        
        # List all objects with the specified prefix
        objects_to_delete = self.client.list_objects(bucket_name, prefix=prefix, recursive=True)
        
        # Create a list of DeleteObject instances
        delete_object_list = [DeleteObject(obj.object_name) for obj in objects_to_delete]
        
        # Delete all objects with the specified prefix
        errors = self.client.remove_objects(bucket_name, delete_object_list)
        
        # Print any errors that occurred during deletion
        for error in errors:
            print(f"Error occurred when deleting object: {error}")
        
        print(f"Deleted all objects under {prefix} in bucket {bucket_name}")

    def load_audio(self, **kwargs):
        """Load and process audio from MinIO."""

        bucket_name = kwargs.get("bucket_name")
        object_name = kwargs.get("object_name")
        
        # Fetch the object from MinIO as a stream
        response = self.client.get_object(bucket_name, object_name)
        
        # Load the audio file from the response stream
        audio_bytes = io.BytesIO(response.read())
        
        return audio_bytes

if __name__ == "__main__":
    minio_service = MinioService()
    
    source_bucket = "public"
    destination_bucket = "data"
    source_folder = "AI_Member/vietdh/data_300h/"
    destination_folder = "ASR/"
    
    # Ensure destination bucket exists
    if not minio_service.client.bucket_exists(destination_bucket):
        minio_service.client.make_bucket(destination_bucket)
    
    # List objects from the source bucket
    objects = minio_service.client.list_objects(source_bucket, prefix=source_folder, recursive=True)
    
    for obj in objects:
        source_object_name = obj.object_name
        # Define the destination object name by keeping the same relative path
        destination_object_name = destination_folder + source_object_name[len(source_folder):]
    
        try:
            # Copy the object
            minio_service.client.copy_object(
                bucket_name=destination_bucket,
                object_name=destination_object_name,
                source=CopySource(source_bucket, source_object_name),
            )
            print(f"Copied {source_object_name} to {destination_object_name}")
        except S3Error as err:
            print(f"Error copying {source_object_name}: {err}")
            