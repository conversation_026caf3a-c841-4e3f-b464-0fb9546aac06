import io
import shutil
from minio import Minio

from .config import Config
import os


class S3Resource:
    def __init__(self):
        self.config = Config()
        self.client = Minio(
            endpoint=self.config.endpoint,
            access_key=self.config.access_key,
            secret_key=self.config.secret_key,
            secure=False
        )

    def get_list_buckets(self):
        buckets = self.client.list_buckets()
        return buckets

    def put(self, **kwargs):
        bucket_name = kwargs.get("bucket_name")
        object_name = kwargs.get("object_name")
        file_path = kwargs.get("file_path")
        result = self.client.fput_object(
            bucket_name=bucket_name,
            object_name=object_name,
            file_path=file_path
        )
        print(f"Created {result.object_name} object; etag: {result.etag}, version: {result.version_id}")

    def get(self, **kwargs):
        
        bucket_name = kwargs.get("bucket_name")
        object_name = kwargs.get("object_name")
        file_path = kwargs.get("file_path")
        self.client.fget_object(
            bucket_name=bucket_name,
            object_name=object_name,
            file_path=file_path
        )
        

    # def delete(self, **kwargs):
    #     bucket_name = kwargs.get("bucket_name")
    #     object_name = kwargs.get("object_name")
    #     errors = self.client.remove_objects(
    #         bucket_name=bucket_name,
    #         delete_object_list=[
    #             DeleteObject(obj)
    #             for obj in object_name
    #         ]
    #     )
    #     for error in errors:
    #         print(f"Error occurred when deleting object: {error}")

    def load_audio(self, **kwargs):
        """Load and process audio from MinIO."""

        bucket_name = kwargs.get("bucket_name")
        object_name = kwargs.get("object_name")

        # Fetch the object from MinIO as a stream
        response = self.client.get_object(bucket_name, object_name)

        # Load the audio file from the response stream
        audio_bytes = io.BytesIO(response.read())

        return audio_bytes

    def load_pretrain(self, **kwargs):
        bucket_name = kwargs.get("bucket_name")
        pretrain = kwargs.get("wav2vec_hub")
        dir_save_s3 = kwargs.get("dir_save_s3")
 
        if os.path.isdir(pretrain):
            print("pretrained is has existed")
            return
        try:
            hub_prefix = os.path.join(dir_save_s3, os.path.basename(pretrain))
            dir_save = pretrain
            # List and download each object in the "checkpoint" folder
            os.makedirs(pretrain, exist_ok=True)
            
            objects = self.client.list_objects(bucket_name, prefix=hub_prefix, recursive=True)
            for obj in objects:
                print("download",obj.object_name)
                # Define the local path for each downloaded file
                adjusted_path = os.path.basename(obj.object_name)
                local_file_path = os.path.join(dir_save, adjusted_path)
                local_dir = os.path.dirname(local_file_path)
                os.makedirs(local_dir, exist_ok=True)  # Create directories as needed

                # Download file
                self.client.fget_object(bucket_name, obj.object_name, local_file_path)
                print(f"Downloaded {obj.object_name} to {local_file_path}")
        except Exception as e:
            # Remove the directory if any exception occurs during the download
            shutil.rmtree(pretrain)
            print(f"An error occurred: {e}. Directory {pretrain} has been removed.")
    def load_lm(self,**kwargs):
        bucket_name = kwargs.get("bucket_name")
        objects = self.client.list_objects(bucket_name, prefix="pvc-7985f026-2905-4c41-87ca-869b58f6e339/models/lm", recursive=True)
        if os.path.isdir("lm_vers/lm"):
            print("lm weight is has existed")
            return

        for obj in objects:
            print("download",obj.object_name)
            # # Define the local path for each downloaded file
            adjusted_path = os.path.basename(obj.object_name)
            local_file_path = os.path.join("lm_vers/lm", adjusted_path)
            local_dir = os.path.dirname(local_file_path)
            os.makedirs(local_dir, exist_ok=True)  # Create directories as needed
            # Download file
            self.client.fget_object(bucket_name, obj.object_name, local_file_path)
            print(f"Downloaded {obj.object_name} to {local_file_path}")
