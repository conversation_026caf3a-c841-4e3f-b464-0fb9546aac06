import os

from .common_keys import *


class Config:

    def __init__(self,
                 endpoint: str = None,
                 access_key: str = None,
                 secret_key: str = None,
                 bucket_name: str = None,
                 object_name: str = None,
                 file_path: str = None
                 ):
        self.endpoint = (
            endpoint
            if endpoint is not None
            else os.getenv(ENDPOINT, "s3.dev.t-idc.net")
        )
        self.access_key = (
            access_key
            if access_key is not None
            else os.getenv(ACCESS_KEY, "HVIu3JVPj8F01q96Gkex")
        )
        self.secret_key = (
            secret_key
            if secret_key is not None
            else os.getenv(SECRET_KEY, "WjV6E4T1r9YFKOj2oBct1zlXuMXeTybWRL20owU4")
        )

        self.bucket_name = bucket_name if bucket_name is not None else os.getenv(ENDPOINT, "models")
        self.object_name = object_name if object_name is not None else os.getenv(ENDPOINT, "lora-vllm-vinallama-chat_3")
        self.file_path = file_path if file_path is not None else os.getenv(ENDPOINT,
                                                                           "../checkpoints/model-00003-of-00003.safetensors")
