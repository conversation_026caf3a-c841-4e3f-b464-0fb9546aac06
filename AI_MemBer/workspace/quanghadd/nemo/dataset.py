from nemo.collections.asr.data.audio_to_text import (
    AudioToBPEDataset,
)
import atexit
import copy
import io
from typing import  Optional
import os
import random
import zipfile
import torchaudio
import torch
from nemo.collections.common.tokenizers import TokenizerSpec
from nemo.collections.common.data.dataset import CodeSwitchedDataset
from nemo.collections.asr.parts.preprocessing.segment import AudioSegment
from omegaconf import OmegaConf, open_dict
from nemo.collections.asr.parts.preprocessing.perturb import (
    AudioAugmentor,
    process_augmentations,
)
import threading
import time
import weakref
from collections.abc import Iterable as IterableABC

from s3_storerity.s3_resource import S3Resource
from nemo.utils import logging

class ZipDataset(AudioToBPEDataset):
    def __init__(self, s3_resource: S3Resource, s3_config, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.s3_resource = s3_resource
        self.s3_config = s3_config
        self.current_gpu = (
            torch.cuda.current_device() if torch.cuda.is_available() else "cpu"
        )
        self.data_folder = self.s3_config.get("data_folder_path", "./data") + str(
            self.current_gpu
        )
        os.makedirs(self.data_folder, exist_ok=True)

        self.zip_groups = self._group_by_zip()
        # logging.info(f"ZipDataset: {len(self.zip_groups)} ZIP files")
        self.zip_filelist = list(self.zip_groups.keys())  # list of ZIP files
        self.reset_epoch()

        self.lock = threading.Lock()
        self.condition = threading.Condition(self.lock)
        self.cache = []
        self.preload_threshold = min(100, len(self.manifest_processor.collection))
        # self.max_cache_size = 500
        self.stop_thread = False
        self.preload_thread = threading.Thread(target=self._preload_loop, daemon=True)
        self._finalizer = weakref.finalize(self, self.shutdown)
        self.max_wait = 90
        if self.zip_filelist:
            self.preload_thread.start()
        atexit.register(self.shutdown)

    def _group_by_zip(self):
        """Group manifest by ZIP files."""
        groups = {}
        for item in self.manifest_processor.collection:
            audio_file = item.audio_file
            if ".zip/" in audio_file:
                # Split to find ZIP path
                zip_file = audio_file.split(".zip/", 1)[0] + ".zip"
                if zip_file not in groups:
                    groups[zip_file] = []
                groups[zip_file].append(item)
            else:
                # Handle non-zip files if needed
                pass
        return groups

    def reset_epoch(self):
        """Reset for new epoch — reshuffle ZIPs."""
        self.unprocessed_zip_files = self.zip_filelist.copy()
        random.shuffle(self.unprocessed_zip_files)
        self.cache = []  # Store samples to consume
        self.state = "idle"

    def __len__(self):
        return len(self.manifest_processor.collection)

    def _process_sample(self, index):

        start_time = time.time()
        audio = None
        sample = None
        sr = 16000
        with self.condition:
            while not self.cache:
                if time.time() - start_time > self.max_wait:
                    logging.error(
                        f"[Timeout] Waiting for sample {index}, cache: {len(self.cache)}, state: {self.state}"
                    )
                    raise TimeoutError(
                        f"Waited too long for cache to fill for sample {index}"
                    )
                self.condition.wait(timeout=0.5)

            sample, audio, sr = self.cache.pop(0)

        audio_segment = AudioSegment(
            samples=audio,
            sample_rate=sr,
            target_sr=16000,
            offset=sample.offset or 0,
            duration=sample.duration,
            trim=self.trim,
            orig_sr=sample.orig_sr,
            channel_selector=self.channel_selector,
        )

        features = self.featurizer.process_segment(audio_segment)
 
        f, fl = features, torch.tensor(features.shape[0]).long()
        t, tl = self.manifest_processor.process_text_by_sample(sample=sample)

        if self.return_sample_id:
            output = f, fl, torch.tensor(t).long(), torch.tensor(tl).long(), index
        else:
            output = f, fl, torch.tensor(t).long(), torch.tensor(tl).long()

        return output

    def _load_next_batch(self, batch=5):
        if not self.unprocessed_zip_files:
            self.reset_epoch()
        take = min(batch, len(self.unprocessed_zip_files))
        files = self.unprocessed_zip_files[:take]
        self.unprocessed_zip_files = self.unprocessed_zip_files[take:]
        for zf_file in files:
            local_file = zf_file.replace(
                self.s3_config.get("data_folder_path", "./data"),
                os.path.join(self.data_folder, ""),
            )
            if not os.path.exists(local_file):
                self.s3_resource.get(
                    bucket_name=self.s3_config.get("bucket_name", "data"),
                    object_name=zf_file,
                    file_path=local_file,
                )
            try:
                with zipfile.ZipFile(local_file, "r") as zf:
                    for item in self.zip_groups[zf_file]:
                        fname = item.audio_file.split(".zip/", 1)[1]
                        with zf.open(fname) as audio_file:
                            audio_bytes = io.BytesIO(audio_file.read())
                            audio, sr = torchaudio.load(audio_bytes)
                            audio = audio[0].cpu().numpy()
                            self.cache.append((item, audio, sr))

            except Exception as e:
                logging.warning(f"Could not load {local_file}: {e}")

            try:
                if os.path.exists(local_file):
                    os.remove(local_file)
            except Exception as e:
                logging.warning(f"Could not delete {local_file}: {e}")

        self.cache.sort(key=lambda x: x[0].duration)

    def _preload_loop(self):
        while not self.stop_thread and self.zip_filelist:
            try:
                cache_size = 0
                with self.condition:
                    cache_size = len(self.cache)

                if cache_size < self.preload_threshold:
                    # Thực hiện tải batch bên ngoài lock để tránh deadlock
                    self._load_next_batch()

                    with self.condition:
                        logging.info(
                            f"ZipDataset preload loop: cache={len(self.cache)}, unprocessed_zip_files={len(self.unprocessed_zip_files)}"
                        )
                        self.condition.notify_all()
                else:
                    # Chỉ chờ khi cache đã đầy
                    with self.condition:
                        self.condition.wait(timeout=0.5)
            except Exception as e:
                logging.error(f"Error in preload loop: {e}")
                time.sleep(1)  # Prevent tight error loop

    def __getitem__(self, index):
        try:
            if index >= len(self.manifest_processor.collection):
                StopIteration(f"Index {index} out of range")
            if isinstance(index, IterableABC):
                return [self._process_sample(_index) for _index in index]
            else:
                return self._process_sample(index)
        except Exception as e:
            logging.warning(f"Error in __getitem__: {e}, index: {index}")
            # Return a dummy sample or raise the exception
            raise e

    def __iter__(self):
        for i in range(len(self)):
            yield self[i]

    def __del__(self):
        self.shutdown()

    def shutdown(self):
        logging.info("ZipDataset: shutdown")
        self.stop_thread = True
        with self.condition:
            self.condition.notify_all()
        if self.preload_thread.is_alive():
            self.preload_thread.join(timeout=2)  #

    def get_status(self):
        """Return current status of the dataset for debugging"""
        return {
            "cache_size": len(self.cache),
            "unprocessed_zip_files": len(self.unprocessed_zip_files),
            "total_zip_files": len(self.zip_filelist),
            "preload_thread_alive": (
                self.preload_thread.is_alive()
                if hasattr(self, "preload_thread")
                else False
            ),
            "stop_thread": self.stop_thread,
        }


def get_code_switched_dataset_s3(
    config: dict,
    global_rank: int,
    world_size: int,
    s3_resource: S3Resource,
    tokenizer: Optional["TokenizerSpec"] = None,
    augmentor: Optional["AudioAugmentor"] = None,
) -> CodeSwitchedDataset:

    if "manifest_filepath" not in config:
        raise ValueError(
            "`manifest_filepath` must be provided in the dataset config if `is_code_switched=True`"
        )
    if "code_switched" not in config:
        raise ValueError(
            "`code_switched` param group must be in the dataset config if `is_code_switched=True`"
        )

    manifest_filepaths = config["manifest_filepath"]
    tarred_audio_filepaths = config.get("tarred_audio_filepaths", None)

    cs_config = OmegaConf.to_container(config["code_switched"])

    # needed to support validation Datasets that arrive here as
    # [[dataset1,dataset2]] otherwise ModelPT would interfere
    if len(manifest_filepaths) == 1 and not isinstance(manifest_filepaths[0], str):
        manifest_filepaths = config["manifest_filepath"][0]
    if tarred_audio_filepaths is None:
        tarred_audio_filepaths = [None] * len(manifest_filepaths)

    if len(manifest_filepaths) != len(tarred_audio_filepaths):
        raise ValueError(
            f"manifest_filepaths (length={len(manifest_filepaths)}) and tarred_audio_filepaths (length={len(tarred_audio_filepaths)}) need to have the same number of items."
        )

    datasets = []
    for dataset_idx, (tarred_audio_filepath, manifest_filepath) in enumerate(
        zip(tarred_audio_filepaths, manifest_filepaths)
    ):
        conf = copy.deepcopy(config)
        conf["manifest_filepath"] = manifest_filepath
        dataset = ZipDataset(
            s3_resource=s3_resource,
            s3_config=conf["s3_config"],
            manifest_filepath=conf["manifest_filepath"],
            tokenizer=tokenizer,
            sample_rate=conf["sample_rate"],
            int_values=conf.get("int_values", False),
            augmentor=None,
            max_duration=conf.get("max_duration", None),
            min_duration=conf.get("min_duration", None),
            max_utts=conf.get("max_utts", 0),
            trim=conf.get("trim_silence", False),
            use_start_end_token=conf.get("use_start_end_token", True),
            return_sample_id=conf.get("return_sample_id", False),
            channel_selector=conf.get("channel_selector", None),
        )

        datasets.append(dataset)

    config = OmegaConf.to_container(config)

    dataset = CodeSwitchedDataset(
        datasets,
        shuffle=cs_config.get("shuffle", True),
        min_duration=cs_config.get("min_duration", 4),
        max_duration=cs_config.get("max_duration", 20),
        min_monolingual=cs_config.get("min_monolingual", 0.3),
        lang_probs=cs_config.get("probs", None),
        db_norm=cs_config.get("db_norm", -25.0),
        pause_start=cs_config.get("pause_start", 0),
        pause_join=cs_config.get("pause_join", 0),
        pause_end=cs_config.get("pause_end", 0),
        sampling_scales=cs_config.get("sampling_scales", None),
        seed=cs_config.get("seed", None),
        global_rank=global_rank,
        world_size=world_size,
        pure_random=cs_config.get("pure_random", False),
        force_monochannel=cs_config.get("force_monochannel", True),
        infinity_mode=cs_config.get("infinity_mode", False),
        sample_rate=config["sample_rate"],
        augmentor=augmentor,
    )

    return dataset


def get_audio_to_text_bpe_dataset_from_config(
    config: dict,
    global_rank: int,
    world_size: int,
    tokenizer: Optional["TokenizerSpec"] = None,
) -> CodeSwitchedDataset | ZipDataset:
    if "augmentor" in config:
        augmentor = process_augmentations(
            config["augmentor"], global_rank=global_rank, world_size=world_size
        )
    else:
        augmentor = None

    if "manifest_filepath" not in config:
        raise ValueError("`manifest_filepath` must be provided in the dataset config")

    s3_resource = S3Resource()
    if config.get("is_code_switched", False):
        return get_code_switched_dataset_s3(
            config, global_rank, world_size, s3_resource, tokenizer, augmentor
        )
    else:
        return ZipDataset(
            s3_resource=s3_resource,
            s3_config=config["s3_config"],
            manifest_filepath=config["manifest_filepath"],
            tokenizer=tokenizer,
            sample_rate=config["sample_rate"],
            int_values=config.get("int_values", False),
            augmentor=None,
            max_duration=config.get("max_duration", None),
            min_duration=config.get("min_duration", None),
            max_utts=config.get("max_utts", 0),
            trim=config.get("trim_silence", False),
            use_start_end_token=config.get("use_start_end_token", True),
            return_sample_id=config.get("return_sample_id", False),
            channel_selector=config.get("channel_selector", None),
        )
