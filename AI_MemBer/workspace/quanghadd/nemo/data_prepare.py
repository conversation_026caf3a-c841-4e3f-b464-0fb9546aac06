import io
import os
import zipfile
from minio import S3Error
from omegaconf import OmegaConf
import pandas as pd
import torchaudio
import tqdm
import logging
import json
from nemo.utils import model_utils
from s3_storerity.s3_resource import S3Resource
logger = logging.getLogger(__name__)


def prepare_data_zip(
    data_folder,
    save_folder,
    s3_resource,
    bucket_name,
    train_splits=[],
    test_splits=[],
    val_splits=[],
    lang="vi",
    min_duration=0.7,
    max_duration=10,
    skip_prep=False,
    percentage_to_select=1.0,
):

    if skip_prep:
        return

    splits = {
        "train": train_splits,
        "test": test_splits,
        "val": val_splits,
    }

    # Tập manifest output
    manifest_data = {
        "train": [],
        "test": [],
        "val": [],
    }

    # Tải về files từ S3
    for split_name, split_folders in splits.items():
        for split in tqdm.tqdm(split_folders, desc=f"Processing {split_name}"):
            directory = os.path.join(data_folder, split)
            if not os.path.exists(directory):
                os.makedirs(directory)

            logger.info("Creating %s ..." % directory)
            # List files từ S3
            try:
                files = list(
                    s3_resource.client.list_objects(
                        bucket_name=bucket_name, prefix=directory + "/", recursive=True
                    )
                )
            except S3Error as e:
                logger.error(e)
                continue

            # Tải về files ZIP
            for obj in tqdm.tqdm(files, desc=f"Processing {split}"):
                if not obj.object_name.endswith(".zip"):
                    continue

                zip_file = os.path.join(directory, os.path.basename(obj.object_name))
                s3_resource.get(
                    bucket_name=bucket_name,
                    object_name=obj.object_name,
                    file_path=zip_file,
                )

                with zipfile.ZipFile(zip_file, "r") as zf:
                    files = zf.namelist()
                    wav_files = {}
                    durations = {}
                    transcript_df = None

                    for fname in files:
                        if fname.endswith(".wav") or fname.endswith(".mp3"):

                            with zf.open(fname) as audio_file:
                                audio_bytes = io.BytesIO(audio_file.read())
                                audio, sr = torchaudio.load(audio_bytes)
                                duration = audio.shape[-1] / sr

                                if (
                                    duration >= min_duration
                                    and duration <= max_duration
                                ):
                                    wav_file = os.path.join(zip_file, fname)

                                    wav_files[os.path.basename(fname)] = wav_file
                                    durations[os.path.basename(fname)] = duration

                        elif fname.endswith(".txt"):
                            with zf.open(fname) as txt_file:
                                df = pd.read_csv(
                                    txt_file,
                                    delimiter=",",
                                    names=["wav", "words"],
                                    header=None,
                                )
                                transcript_df = df.copy()

                    if transcript_df is not None:
                        transcript_df = transcript_df[~transcript_df["words"].isnull()]
                        transcript_df = transcript_df[
                            transcript_df["wav"].isin(wav_files.keys())
                        ].copy()
                        transcript_df["ID"] = transcript_df["wav"].str.replace(
                            ".wav", "", regex=False
                        )
                        transcript_df["duration"] = transcript_df["wav"].map(durations)
                        transcript_df["wav"] = transcript_df["wav"].map(wav_files)

                        num_data_to_select = int(
                            percentage_to_select * len(transcript_df)
                        )
                        transcript_df = transcript_df.sample(n=num_data_to_select)

                        for row in transcript_df.itertuples():
                            manifest_data[split_name].append(
                                {
                                    "audio_filepath": row.wav,
                                    "text": row.words,
                                    "duration": row.duration,
                                    "lang": lang,
                                }
                            )

                if os.path.exists(zip_file):
                    os.remove(zip_file)

    # Ghi manifest files
    if not os.path.exists(save_folder):
        os.makedirs(save_folder)

    for split_name, data in manifest_data.items():
        manifest_file = os.path.join(save_folder+lang, f"{split_name}_manifest.json")
        directory = os.path.dirname(manifest_file)
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
        logger.info("Writing manifest to %s ..." % manifest_file)
        with open(manifest_file, "w") as f:
            for item in data:
                f.write(json.dumps(item, ensure_ascii=False) + "\n")

    logger.info("prepare_data: DONE!")


if __name__ == "__main__":

    s3_resource = S3Resource()
    cfg = OmegaConf.load("fastconformer_hybrid_tdt_ctc_bpe.yaml")
    cfg = model_utils.convert_model_config_to_dict_config(cfg)
    s3_config = cfg.model.s3_config
    # # prepare VI
    # prepare_data_zip(
    #     data_folder=s3_config["data_folder_path"],
    #     save_folder=cfg.model["save_folder"],
    #     s3_resource=s3_resource,
    #     bucket_name=s3_config["bucket_name"],
    #     train_splits=s3_config.get("train_vi_splits", []),
    #     test_splits=s3_config.get("test_splits",[]) ,
    #     val_splits=s3_config.get("val_splits",[]),
    #     lang="vi",
    #     min_duration=cfg.model.train_ds.min_duration,
    #     max_duration=cfg.model.train_ds.max_duration,
    #     skip_prep=False,
    #     percentage_to_select=1.0,
    # )
    # prepare EN
    prepare_data_zip(
        data_folder=s3_config["data_folder_path"],
        save_folder=cfg.model["save_folder"],
        s3_resource=s3_resource,
        bucket_name=s3_config["bucket_name"],
        train_splits=s3_config.get("train_en_splits", []),
        test_splits=[],
        val_splits=[],
        lang="en",
        min_duration=cfg.model.train_ds.min_duration,
        max_duration=cfg.model.train_ds.max_duration,
        skip_prep=False,
        percentage_to_select=1.0,
    )
