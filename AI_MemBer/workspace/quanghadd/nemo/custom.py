


from transformers import Wav2Vec2CTCTokenizer
from nemo.collections.common.tokenizers import TokenizerSpec
from typing import List, Union, Optional
import numpy as np
import torch




class Wav2Vec2WordpieceTokenizer(Wav2Vec2CTCTokenizer):
    def __init__(
        self,
        vocab_file,
        bos_token="<s>",
        eos_token="</s>",
        unk_token="<unk>",
        pad_token="<pad>",
        word_delimiter_token="|",
        do_lower_case=False,
        **kwargs,
    ):
        super().__init__(
            vocab_file=vocab_file,
            unk_token=unk_token,
            bos_token=bos_token,
            eos_token=eos_token,
            pad_token=pad_token,
            do_lower_case=do_lower_case,
            word_delimiter_token=word_delimiter_token,
            **kwargs,
        )
        try:
            self._create_trie(self.all_special_tokens_extended)
        except:
            self._update_trie(self.all_special_tokens_extended)

    # def _create_trie(self, unique_no_split_tokens):
    #     trie = Trie()
    #     for token in unique_no_split_tokens:
    #         # nếu token chưa là str -> <PERSON><PERSON><PERSON><PERSON> sang str
    #         token = str(token)
    #         if hasattr(self, "do_lower_case") and self.do_lower_case and token not in self.all_special_tokens:
    #             trie.add(token.lower())  # thêm vào trie
    #         else:
    #             trie.add(token)
    #     self.tokens_trie = trie

    def _tokenize(self, text, **kwargs):
        """
        Converts a string in a sequence of tokens (string), using the tokenizer.
        """
        special_cases = set(["gia", "qui", "quy", "que", "qua"])
        output_tokens = []
        for token_idx, token in enumerate(text.split()):
            if token in special_cases:
                sub_tokens = [token[:2], token[2:]]
            else:
                end = len(token)
                sub_tokens = []
                while end > 0:
                    start = 0
                    cur_substr = None
                    while start < end:
                        substr = token[start:end]
                        if substr in self.encoder:
                            cur_substr = substr
                            break
                        start += 1
                    if cur_substr is None:
                        sub_tokens.insert(0, self.unk_token)
                        end = start - 1
                    else:
                        sub_tokens.insert(0, cur_substr)
                        end = start

            if token_idx > 0:
                output_tokens.append(self.word_delimiter_token)
            output_tokens.extend(sub_tokens)
        return output_tokens

    def decode_ids(
        self,
        token_ids,
        skip_special_tokens=False,
        clean_up_tokenization_spaces=True,
        group_tokens: bool = True,
        spaces_between_special_tokens: bool = False,
    ) -> str:
        # For compatible with speechbrain interfaces
        return self.decode(
            token_ids,
            skip_special_tokens=skip_special_tokens,
            clean_up_tokenization_spaces=clean_up_tokenization_spaces,
            group_tokens=group_tokens,
            spaces_between_special_tokens=spaces_between_special_tokens,
        )


class HFTokenizerAdapter(TokenizerSpec):
    def __init__(self, dir: str, vocab_path: str = None):

        self.tokenizer = Wav2Vec2WordpieceTokenizer.from_pretrained(dir)
        self.vocab = self.tokenizer.get_vocab()

        self.pad_id = self.tokenizer.pad_token_id or 0
        self.blank_id = 0
        self.bos_id = self.tokenizer.bos_token_id or 1
        self.eos_id = self.tokenizer.eos_token_id or 2
        self.unk_id = self.tokenizer.unk_token_id or 3
        self.word_delimiter_token_id = self.tokenizer.word_delimiter_token_id or 4
        self.ids__tokens = {v: (" " if k == "|" else k) for k, v in self.vocab.items()}
        self.ids__tokens[self.word_delimiter_token_id] = " "
        self.ids__tokens[self.pad_id] = ""

    def text_to_ids(self, text):
        subtokens = self.tokenizer._tokenize(text)
        tokens_list = self.tokenizer.convert_tokens_to_ids(subtokens)
        return tokens_list

    def ids_to_text(self, ids):
        return self.tokenizer.decode_ids(ids)

    def get_vocab(self):
        return self.vocab

    def ids_to_tokens(self, ids):
        tokens = [self.ids__tokens[id] for id in ids]
        return tokens

    def text_to_tokens(self, text):
        subtokens = self.tokenizer._tokenize(text)
        return subtokens

    def tokens_to_ids(
        self, tokens: Union[str, List[str]], tokens_to_skip: List[str] = []
    ) -> Union[int, List[int]]:
        if isinstance(tokens, str):
            tokens = [tokens]
        ids = []
        for token in tokens:
            if token not in tokens_to_skip:
                ids.append(self.tokenizer.convert_tokens_to_ids(token))
        return ids

    def tokens_to_text(self, tokens):
        if isinstance(tokens, (np.ndarray, torch.Tensor)):
            tokens = tokens.tolist()
        return self.tokenizer.convert_tokens_to_string(tokens)

