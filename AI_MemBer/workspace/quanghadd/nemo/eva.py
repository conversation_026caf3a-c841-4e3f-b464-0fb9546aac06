import time
import pandas as pd
from evaluate import load
import os
import librosa
import tqdm
from omegaconf import OmegaConf, open_dict

from asr_model.model import ASRModel
wer = load("wer")
class Inferencer:
    def  __init__(self, model_path, device) -> None:
        self.model_path = model_path
        self.device = device
        # self.model:ASRModel = ASRModel.restore_from(restore_path=model_path, map_location=device)
        self.model:ASRModel = ASRModel.load_from_checkpoint(checkpoint_path="asr_model/parakeet-tdt-0.6-vn--val_wer=0.0651-epoch=1-last.ckpt",map_location='cpu')
        self.model.eval()
        self.model.to(device)
       
        decoding_cfg = self.model.cfg.decoding
        with open_dict(decoding_cfg):
            # decoding_cfg.strategy = 'greedy_batch'
            decoding_cfg.strategy = 'beam'
            decoding_cfg.beam.beam_size=2
            self.model.change_decoding_strategy(decoding_cfg)
        
    def load_data(self, path: str, delimiter: str) -> pd.DataFrame:
        df = pd.read_csv(path, delimiter = delimiter)
        return df

    def write_data(self, result, output_text_path,wer_out):
        result.to_csv(output_text_path, index=None)
        with open(output_text_path,"a") as file:
            file.write(f"WER: {wer_out}")

    def compute_wer(self, result):
        wer_out = 100 * wer.compute(predictions=result["pred_transcript"], references=result["transcript"])
        print("WER: {:2f}".format(wer_out))
        return wer_out

    def run(self, folder_path: str, delimiter: str, type_data: str, model_version: str):
        if(type_data==""):
            text_path = folder_path + f"eva.txt"
        else:
            text_path = folder_path + f"eva_{type_data}.txt"
            
        # output_text_path = text_path.replace(".txt", f"_{model_version}.txt")
        output_text_path =  f"results_eva/{type_data}_{model_version}.txt"
        batch = self.load_data(text_path, delimiter)
        lines = len(batch)
        
        total_time=[]
        total_lenght=[]
        for line in tqdm.tqdm(range(lines)):
            if type_data == "vivos":
                vivos_folder = batch["path"][line].split("_")[0]
                audio_path = folder_path + f"wav/" + vivos_folder + "/" + batch["path"][line]
            else:
                audio_path = folder_path + f"wav/" + batch["path"][line]
            audio, _ = librosa.load(audio_path, sr = 16000)    
            start=time.time()
            hypotheses = self.model.transcribe([audio_path])
            if type(hypotheses[0]) == list:
               transcript=hypotheses[0][0].text
            else:
                transcript=hypotheses[0].text
            runtime=time.time()-start
            total_time.append(runtime)
            total_lenght.append(audio.shape[-1]/16000)

            batch["pred_transcript"][line] = transcript
        print("RTFx", round(sum(total_lenght)/sum(total_time),2))
        wer_out=self.compute_wer(batch)
        self.write_data(batch, output_text_path,wer_out)
        return wer_out

if __name__ == '__main__':
    os.environ["CUDA_VISIBLE_DEVICES"]="1"
    device = 'cuda'
    # device = "cpu"
    model_version = "160625"
    model_path = f"/TMTAI/AI_MemBer/workspace/quanghadd/nemo/asr_model/parakeet-tdt-0.6-vn.nemo"
    
    type_data = ""
    # folder_path = f"/TMTAI/AI_MemBer/workspace/quanghadd/Wav2vec-270h//audio_test/Call_Center_Customer_Batch_1/test/test_{type_data}/"
    folder_path = f"/TMTAI/AI_MemBer/workspace/quanghadd/Wav2vec-270h/audio_test/ytb_en_vi/"

    
    inferencer = Inferencer(

                    model_path=model_path, 
                    device = device
                )
    
    inferencer.run(folder_path, ",", type_data, model_version)
   