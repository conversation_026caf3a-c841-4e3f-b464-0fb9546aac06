{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "from minio import Minio\n", "from minio.error import S3Error\n", "\n", "client = Minio(\n", "    endpoint=\"s3.dev.t-idc.net\",\n", "    access_key=\"HVIu3JVPj8F01q96Gkex\",\n", "    secret_key=\"WjV6E4T1r9YFKOj2oBct1zlXuMXeTybWRL20owU4\",\n", "    secure=False,\n", ")\n", "bucket_name = \"ai-asr\"\n", "local_folder = \"/TMTAI/AI_MemBer/workspace/quanghadd/nemo\"\n", "minio_prefix = \"ASR/asr-quanghadd/nemo\"  # Đường dẫn đích trên <PERSON>\n", "\n", "# <PERSON>yệt và upload tất cả file trong thư mục\n", "for root, dirs, files in os.walk(local_folder):\n", "    for file in files:\n", "        local_path = os.path.join(root, file)\n", "        relative_path = os.path.relpath(local_path, local_folder)\n", "        # Tạo đường dẫn đầy đủ trên MinIO\n", "        minio_path = os.path.join(minio_prefix, relative_path).replace(\"\\\\\", \"/\")\n", "        client.fput_object(bucket_name, minio_path, local_path)\n", "        print(f\"Uploaded: {local_path} → {bucket_name}/{minio_path}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ASR/asr-quanghadd/\n", "ASR/zip/\n"]}], "source": ["bucket_name = \"ai-asr\"\n", "prefix = \"ASR/\"\n", "objects = client.list_objects(bucket_name, prefix, recursive=False)\n", "for obj in objects:\n", "    print(obj.object_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "from minio import Minio\n", "\n", "client = Minio(\n", "    endpoint=\"s3.dev.t-idc.net\",\n", "    access_key=\"HVIu3JVPj8F01q96Gkex\",\n", "    secret_key=\"WjV6E4T1r9YFKOj2oBct1zlXuMXeTybWRL20owU4\",\n", "    secure=False,\n", ")\n", "bucket_name = \"ai-asr\"\n", "minio_prefix = \"ASR/asr-quanghadd/nemo/\"\n", "local_folder = \"./nemo\"  # thư mục đích local\n", "\n", "# <PERSON><PERSON>y danh sách các object có prefix chỉ định\n", "objects = client.list_objects(bucket_name, prefix=minio_prefix, recursive=True)\n", "\n", "for obj in objects:\n", "    # Tạo đường dẫn local tương ứng\n", "    relative_path = obj.object_name[len(minio_prefix) :]\n", "    local_path = os.path.join(local_folder, relative_path)\n", "\n", "    # <PERSON><PERSON><PERSON> thư mục nếu chưa có\n", "    os.makedirs(os.path.dirname(local_path), exist_ok=True)\n", "\n", "    # T<PERSON><PERSON> file\n", "    client.fget_object(bucket_name, obj.object_name, local_path)\n", "    print(f\"Downloaded: {obj.object_name} → {local_path}\")"]}], "metadata": {"kernelspec": {"display_name": "quanghadd_nemo", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}