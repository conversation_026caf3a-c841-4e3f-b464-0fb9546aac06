name: "parakeet-tdt-0.6-vn"
init_from_nemo_model: asr_model/parakeet-tdt-0.6b-v2/parakeet-tdt-0.6b-v2.nemo
model:
  sample_rate: 16000
  compute_eval_loss: false
  log_prediction: false  ### Hiện thị log inference WER
  rnnt_reduction: mean_volume
  skip_nan_grad: false
  use_cer: false
  model_defaults:
    enc_hidden:  ${model.encoder.d_model}
    pred_hidden: 640
    joint_hidden: 640
    tdt_durations:
    - 0
    - 1
    - 2
    - 3
    - 4
    num_tdt_durations: 5

  seed: 160625
  save_folder: "results/${model.seed}/"
  s3_config:
    bucket_name: "ai-asr"
    data_folder_path: ASR/zip/
    train_vi_splits: [film_live_new, Call_Center_Batch_2, Call_Center_Batch_3, podcast,
                    podcast_1, VinBigdata/VinBigdata-VLSP2020-100h, VinBigdata/VLSP_2021/train, Call_Center_Customer_Batch_1/Data/train/train_clean,
                    Call_Center_Customer_Batch_2,CommonVoice/vi/train,FOSD,VinBigdata/ASR_VLSP_2020_train,vivos/train]
    train_en_splits: [librispeech]
    val_splits: [Multilingual_vi_en/ytb_en_vi,CommonVoice/vi/dev,vivos/dev]
    test_splits: [CommonVoice/vi/test,VinBigdata/VLSP_2021/test/task1, VinBigdata/VLSP_2021/test/task2,
                Multilingual_vi_en/ytb_en_vi]

  train_ds:
    manifest_filepath: ["${model.save_folder}/vi/train_manifest.json","${model.save_folder}/en/train_manifest.json"]
    # manifest_filepath: [train/vi_manifest.json,train/en_manifest.json]
    sample_rate: ${model.sample_rate}
    skip_if_error: false
    skip_missing_manifest_entries: False
    is_custom: true
    s3_config: ${model.s3_config}
    is_code_switched: true
    code_switched:
      min_duration: 3.0
      max_duration: 8.0
      min_monolingual: 0.5
      probs: [0.8, 0.2]
    batch_size: 2
    shuffle: false
    num_workers: 0
    pin_memory: true
    min_duration: 0.7
    max_duration: 8.0
    shuffle_n: 2048
    # bucketing params
    bucketing_strategy: "synced_randomized"
    bucketing_batch_size: null
  
  validation_ds:
    is_custom: true
    s3_config: ${model.s3_config}
    manifest_filepath: "${model.save_folder}/vi/val_manifest.json"
    sample_rate: ${model.sample_rate}
    batch_size: 1
    shuffle: false
    use_start_end_token: false
    num_workers: 0
    pin_memory: true

  test_ds:
    is_custom: true
    s3_config: ${model.s3_config}
    manifest_filepath: "${model.save_folder}/vi/test_manifest.json"
    sample_rate: ${model.sample_rate}
    batch_size: 1
    shuffle: false
    use_start_end_token: false
    num_workers: 0
    pin_memory: true

  tokenizer_agg:
    type: agg
    langs:
      en:
        type: bpe
        dir: tokenizer_en/
        model_path: tokenizer_en/tokenizer.model
        vocab_path: tokenizer_en/vocab.txt
        spe_tokenizer_vocab: tokenizer_en/tokenizer.vocab
      vi:
        type: wpe
        dir: tokenizer_vi
        vocab_path: tokenizer_vi/vocab.txt

  tokenizer:
    type: bpe
    dir: tokenizer_en/


  preprocessor:
    _target_: nemo.collections.asr.modules.AudioToMelSpectrogramPreprocessor
    sample_rate: ${model.sample_rate}
    normalize: per_feature
    window_size: 0.025
    window_stride: 0.01
    window: hann
    features: 128
    n_fft: 512
    log: true
    frame_splicing: 1
    dither: 1.0e-05
    pad_to: 0
    pad_value: 0.0

  spec_augment:
    _target_: nemo.collections.asr.modules.SpectrogramAugmentation
    freq_masks: 2
    time_masks: 10
    freq_width: 27
    time_width: 0.05

  encoder:
    _target_: nemo.collections.asr.modules.ConformerEncoder
    feat_in: ${model.preprocessor.features}
    feat_out: -1
    n_layers: 24
    d_model: 1024
    use_bias: false
    subsampling: dw_striding
    subsampling_factor: 8
    subsampling_conv_channels: 256
    causal_downsampling: false
    reduction: null
    reduction_position: null
    reduction_factor: 1
    ff_expansion_factor: 4
    self_attention_model: rel_pos
    n_heads: 8
    att_context_size:
    - -1
    - -1
    att_context_style: regular
    xscaling: false
    untie_biases: true
    pos_emb_max_len: 5000
    conv_kernel_size: 9
    conv_norm_type: batch_norm
    conv_context_size: null
    dropout: 0.1
    dropout_pre_encoder: 0.1
    dropout_emb: 0.0
    dropout_att: 0.1
    stochastic_depth_drop_prob: 0.0
    stochastic_depth_mode: linear
    stochastic_depth_start_layer: 1

  decoder:
    _target_: nemo.collections.asr.modules.RNNTDecoder
    normalization_mode: null
    random_state_sampling: false
    blank_as_pad: true

    prednet:
      pred_hidden: ${model.model_defaults.pred_hidden}
      pred_rnn_layers: 2
      t_max: null
      dropout: 0.2
    # vocab_size: 1024

  joint:
    _target_: nemo.collections.asr.modules.RNNTJoint
    log_softmax: null
    preserve_memory: false
    fuse_loss_wer: true
    fused_batch_size: 4

    jointnet:
      joint_hidden:  ${model.model_defaults.joint_hidden}
      activation: relu
      dropout: 0.2
      encoder_hidden: ${model.encoder.d_model}
      pred_hidden: ${model.model_defaults.pred_hidden}

    num_extra_outputs: ${model.model_defaults.num_tdt_durations}
    # num_classes: ${model.decoder.vocab_size}
    # vocabulary:
    # - wer

  decoding:
    strategy: greedy_batch
    model_type: tdt
    durations: ${model.model_defaults.tdt_durations}

    greedy:
      max_symbols: 10
    beam:
      beam_size: 2
      return_best_hypothesis: false
      score_norm: true
      tsd_max_sym_exp: 50
      alsd_max_target_len: 2.0
  aux_ctc:
    ctc_loss_weight: 0.3
    use_cer: false
    ctc_reduction: mean_batch
    decoder:
      _target_: nemo.collections.asr.modules.ConvASRDecoder
      feat_in: null
      num_classes: -1
      vocabulary: []
    decoding:
      strategy: greedy
  interctc:
    loss_weights: []
    apply_at_layers: []
  loss:
    loss_name: tdt
    tdt_kwargs:
      fastemit_lambda: 0.0
      clamp: -1.0
      durations: ${model.model_defaults.tdt_durations}
      sigma: 0.02
      omega: 0.1
  optim:
    name: adamw
    lr: 0.1
    betas:
    - 0.9
    - 0.98
    weight_decay: 0.001
    sched:
      name: NoamAnnealing
      d_model: ${model.encoder.d_model}
      warmup_steps: 10000
      warmup_ratio: null
      min_lr: 1e-6


trainer:
  devices: -1 # number of GPUs, -1 would use all available GPUs
  num_nodes: 1
  max_epochs: 500
  max_steps: -1 # computed at runtime if not set
  val_check_interval: 0.25 # Set to 0.25 to check 4 times per epoch, or an int for number of iterations
  accelerator: gpu
  strategy:
    _target_: lightning.pytorch.strategies.DDPStrategy
    gradient_as_bucket_view: true
  accumulate_grad_batches: 4 ### tương đương với gradient_accumulation trong speechbrain
  gradient_clip_val: 1.0
  precision: 32 # 16, 32, or bf16
  log_every_n_steps: 10  # Interval of logging.
  enable_progress_bar: True
  num_sanity_val_steps: 0 # number of steps to perform validation steps for sanity check the validation process before starting the training, setting to 0 disables it
  check_val_every_n_epoch: 1 # number of evaluations on validation every n epochs
  sync_batchnorm: true
  enable_checkpointing: False  # Provided by exp_manager
  logger: false  # Provided by exp_manager
  benchmark: false # needs to be false for models with variable-length speech input as it slows down training
  default_root_dir: ${model.save_folder}
  # use_distributed_sampler: false
  # limit_train_batches: 1000
  
exp_manager:
  exp_dir: ${model.save_folder}
  name: ${name}
  version: "2025-07-07_01-57-40"
  create_tensorboard_logger: true
  create_checkpoint_callback: true
  checkpoint_callback_params:
    # in case of multiple validation sets, first one is used
    monitor: "val_wer"
    mode: "min"
    save_top_k: 2
    always_save_nemo: True # saves the checkpoints as nemo files instead of PTL checkpoints
    
  resume_from_checkpoint: null
  resume_if_exists: true
  resume_ignore_no_checkpoint: false
  
  create_mlflow_logger: True
  mlflow_logger_kwargs:
    experiment_name: ${name}.mulltilingual-20250618-0
    tags:
      owner: quang
      task: ASR
      language: multilingual
    # save_dir: './mlruns'
    tracking_uri: 'https://mlflow.ai.rke.app.dev.tmtco.org'
    prefix: ''
    artifact_location: None
    log_model: true
    # provide run_id if resuming a previously started run
    run_id: null
    # run_name: null
