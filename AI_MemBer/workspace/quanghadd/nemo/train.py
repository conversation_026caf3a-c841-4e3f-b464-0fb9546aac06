# Fine-tune a model
"""
For documentation on fine-tuning this model, please visit -
https://docs.nvidia.com/deeplearning/nemo/user-guide/docs/en/main/asr/configs.html#fine-tuning-configurations

"""

import lightning.pytorch as pl
import torch
from omegaconf import OmegaConf, DictConfig, open_dict
from nemo.collections.asr.models import EncDecRNNTBPEModel
from nemo.core.config import hydra_runner
from nemo.utils import logging, model_utils
# from nemo.utils.exp_manager import exp_manager
from  exp_manager import exp_manager
from nemo.utils.trainer_utils import resolve_trainer_cfg
import os
from custom import HFTokenizerAdapter
from dataset import get_audio_to_text_bpe_dataset_from_config
from typing import List, Union, Optional, Dict
from nemo.collections.common import tokenizers

from nemo.core.classes.common import  typecheck

class ASRModel(EncDecRNNTBPEModel):

    def _setup_aggregate_tokenizer(self, tokenizer_cfg: DictConfig):
        # Prevent tokenizer parallelism (unless user has explicitly set it)
        if "TOKENIZERS_PARALLELISM" not in os.environ:
            os.environ["TOKENIZERS_PARALLELISM"] = "false"

        self.tokenizer_cfg = OmegaConf.to_container(
            tokenizer_cfg, resolve=True
        )  # type: dict

        # the aggregate tokenizer does not have one tokenizer_dir but multiple ones
        self.tokenizer_dir = None

        self.tokenizer_cfg.pop("dir", None)  # Remove tokenizer directory, if any
        # Remove tokenizer_type -- obviously if we are here, the type is 'agg'
        self.tokenizer_type = self.tokenizer_cfg.pop("type").lower()

        # the aggregate tokenizer should not have these
        self.hf_tokenizer_kwargs = {}
        self.tokenizer_cfg.pop("hf_kwargs", {})  # Remove HF tokenizer kwargs, if any

        logging.info("_setup_tokenizer: detected an aggregate tokenizer")
        # need to de-register any monolingual config items if they exist
        self._cleanup_monolingual_and_aggregate_config_and_artifacts_if_needed()

        # overwrite tokenizer type
        if hasattr(self, "cfg") and "tokenizer" in self.cfg:
            self.cfg.tokenizer = self.tokenizer_cfg
        tokenizers_dict = {}
        # init each of the monolingual tokenizers found in the config and assemble into  AggregateTokenizer
        for lang, tokenizer_config in self.tokenizer_cfg[
            self.AGGREGATE_TOKENIZERS_DICT_PREFIX
        ].items():
            (
                tokenizer,
                model_path,
                vocab_path,
                spe_vocab_path,
            ) = (
                self._make_tokenizer(tokenizer_config, lang)
                if lang != "vi"
                else (
                    HFTokenizerAdapter(tokenizer_config["dir"]),
                    None,
                    None,
                    None,
                )
            )

            tokenizers_dict[lang] = tokenizer
            if hasattr(self, "cfg"):
                with open_dict(self.cfg.tokenizer):
                    self.cfg.tokenizer[self.AGGREGATE_TOKENIZERS_DICT_PREFIX][lang][
                        "dir"
                    ] = self.tokenizer_cfg[self.AGGREGATE_TOKENIZERS_DICT_PREFIX][lang][
                        "dir"
                    ]
                    self.cfg.tokenizer[self.AGGREGATE_TOKENIZERS_DICT_PREFIX][lang][
                        "type"
                    ] = self.tokenizer_cfg[self.AGGREGATE_TOKENIZERS_DICT_PREFIX][lang][
                        "type"
                    ]

        if "custom_tokenizer" in tokenizer_cfg:
            # Class which implements this is usually a ModelPT, has access to Serializable mixin by extension
            self.tokenizer = self.from_config_dict(
                {
                    "_target_": tokenizer_cfg["custom_tokenizer"]["_target_"],
                    "tokenizers": tokenizers_dict,
                }
            )
        else:
            self.tokenizer = tokenizers.AggregateTokenizer(tokenizers_dict)

    def setup_training_data(self, train_data_config):
        self._shutdown_dataset(self._train_dl)
        super().setup_training_data(train_data_config)

    def setup_validation_data(self, val_data_config):
        self._shutdown_dataset(self._validation_dl)
        super().setup_validation_data(val_data_config)

    def setup_test_data(self, test_data_config):
        self._shutdown_dataset(self._test_dl)
        super().setup_test_data(test_data_config)

    def _setup_dataloader_from_config(self, config: Optional[Dict]):
        if config.get("is_custom", False):
            dataset = get_audio_to_text_bpe_dataset_from_config(
                config=config,
                global_rank=self.global_rank,
                world_size=self.world_size,
                tokenizer=self.tokenizer,
            )
            shuffle = config["shuffle"]
            if isinstance(dataset, torch.utils.data.IterableDataset):
                shuffle = False
            dataloader = torch.utils.data.DataLoader(
                dataset=dataset,
                batch_size=config["batch_size"],
                # sampler=batch_sampler,
                batch_sampler=None,
                collate_fn=dataset.collate_fn,
                drop_last=config.get("drop_last", False),
                shuffle=shuffle,
                num_workers=config.get("num_workers", 0),
                pin_memory=config.get("pin_memory", False),
            )
        else:
            dataloader = super()._setup_dataloader_from_config(config)
        return dataloader

    def on_train_end(self):
        super().on_train_end()
        self._shutdown_dataset(self._train_dl)
        self._shutdown_dataset(self._validation_dl)

    def on_test_end(self):
        super().on_test_end()
        self._shutdown_dataset(self._test_dl)

    def _shutdown_dataset(self, dataloader):
        if dataloader is None:
            return
        dataset = dataloader.dataset

        if isinstance(dataset, (list, tuple)):
            for d in dataset:
                if hasattr(d, "shutdown") and callable(d.shutdown):
                    d.shutdown()
        elif hasattr(dataset, "datasets") and isinstance(
            dataset.datasets, (list, tuple)
        ):
            for d in dataset.datasets:
                if hasattr(d, "shutdown") and callable(d.shutdown):
                    d.shutdown()
        if hasattr(dataset, "shutdown") and callable(dataset.shutdown):
            dataset.shutdown()
    
    @typecheck()
    def forward(
        self, input_signal=None, input_signal_length=None, processed_signal=None, processed_signal_length=None
    ):
        """
        Forward pass of the model. Note that for RNNT Models, the forward pass of the model is a 3 step process,
        and this method only performs the first step - forward of the acoustic model.

        Please refer to the `training_step` in order to see the full `forward` step for training - which
        performs the forward of the acoustic model, the prediction network and then the joint network.
        Finally, it computes the loss and possibly compute the detokenized text via the `decoding` step.

        Please refer to the `validation_step` in order to see the full `forward` step for inference - which
        performs the forward of the acoustic model, the prediction network and then the joint network.
        Finally, it computes the decoded tokens via the `decoding` step and possibly compute the batch metrics.

        Args:
            input_signal: Tensor that represents a batch of raw audio signals,
                of shape [B, T]. T here represents timesteps, with 1 second of audio represented as
                `self.sample_rate` number of floating point values.
            input_signal_length: Vector of length B, that contains the individual lengths of the audio
                sequences.
            processed_signal: Tensor that represents a batch of processed audio signals,
                of shape (B, D, T) that has undergone processing via some DALI preprocessor.
            processed_signal_length: Vector of length B, that contains the individual lengths of the
                processed audio sequences.

        Returns:
            A tuple of 2 elements -
            1) The log probabilities tensor of shape [B, T, D].
            2) The lengths of the acoustic sequence after propagation through the encoder, of shape [B].
        """
        has_input_signal = input_signal is not None and input_signal_length is not None
        has_processed_signal = processed_signal is not None and processed_signal_length is not None
        if (has_input_signal ^ has_processed_signal) is False:
            raise ValueError(
                f"{self} Arguments ``input_signal`` and ``input_signal_length`` are mutually exclusive "
                " with ``processed_signal`` and ``processed_signal_len`` arguments."
            )

        if not has_processed_signal:
            input_signal_length = torch.clamp(input_signal_length, min=1120)
            processed_signal, processed_signal_length = self.preprocessor(
            input_signal=input_signal,
            length=input_signal_length,
            )

        # Spec augment is not applied during evaluation/testing
        if self.spec_augmentation is not None and self.training:
            processed_signal = self.spec_augmentation(input_spec=processed_signal, length=processed_signal_length)

        encoded, encoded_len = self.encoder(audio_signal=processed_signal, length=processed_signal_length)
        return encoded, encoded_len

# @hydra_runner(config_path="./", config_name="fastconformer_hybrid_tdt_ctc_bpe")
def main(cfg=None):
    
    os.environ["MLFLOW_TRACKING_LOG_LEVEL"] = "WARNING"
    os.environ["MLFLOW_S3_IGNORE_TLS"] = "true"
    os.environ["AWS_DEFAULT_REGION"] = "vietnam"
    os.environ["MLFLOW_TRACKING_URI"] = "https://mlflow.ai.rke.app.dev.tmtco.org"
    os.environ["MLFLOW_S3_ENDPOINT_URL"] = "http://s3.dev.t-idc.net"
    os.environ["AWS_SECRET_ACCESS_KEY"] = "WjV6E4T1r9YFKOj2oBct1zlXuMXeTybWRL20owU4"
    os.environ["AWS_ACCESS_KEY_ID"] = "HVIu3JVPj8F01q96Gkex"

    os.environ["CUDA_VISIBLE_DEVICES"] = "1,2,3"
    
    cfg=OmegaConf.load('fastconformer_hybrid_tdt_ctc_bpe.yaml')
    cfg = model_utils.convert_model_config_to_dict_config(cfg)

    cfg['trainer']['devices']=3
    cfg['trainer']['accelerator']='gpu'

    trainer = pl.Trainer(**resolve_trainer_cfg(cfg.trainer))
    exp_manager(trainer, cfg.get("exp_manager", None))
    
    asr_model=ASRModel.restore_from(cfg.init_from_nemo_model,cfg.model)
    
    # asr_model.change_vocabulary(cfg.model.tokenizer_agg,'agg')
    asr_model.setup_training_data(cfg.model.train_ds)
    asr_model.setup_validation_data(cfg.model.validation_ds)
    asr_model.setup_test_data(cfg.model.test_ds)
    trainer.fit(asr_model)

    if hasattr(cfg.model, 'test_ds') and cfg.model.test_ds.manifest_filepath is not None:
        if asr_model.prepare_test(trainer):
            trainer.test(asr_model)


if __name__ == '__main__':

    main()  # noqa pylint: disable=no-value-for-parameter
